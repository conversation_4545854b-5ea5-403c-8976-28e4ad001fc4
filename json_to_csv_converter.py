#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON to CSV Converter for Staff CV Data
将transformed_all.json转换为CSV格式
"""

import json
import csv
import pandas as pd
from typing import List, Dict, Any

def load_json_data(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def convert_to_csv_format(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    将JSON数据转换为CSV格式
    每个技能/项目作为一行，包含所有相关信息
    """
    csv_rows = []
    
    for staff in data:
        staff_code = staff.get('staff_code', '')
        staff_cv = staff.get('staff_cv', [])
        
        # 如果没有CV数据，创建一行空记录
        if not staff_cv:
            csv_rows.append({
                'staff_code': staff_code,
                'category': '',
                'level_1': '',
                'level_2': '',
                'level_3': '',
                'details': '',
                'business_family': '',
                'business_household': ''
            })
        else:
            # 为每个CV条目创建一行
            for cv_item in staff_cv:
                row = {
                    'staff_code': staff_code,
                    'category': cv_item.get('category', ''),
                    'level_1': cv_item.get('level_1', ''),
                    'level_2': cv_item.get('level_2', ''),
                    'level_3': cv_item.get('level_3', ''),
                    'details': cv_item.get('details', ''),
                    'business_family': cv_item.get('business_family', ''),
                    'business_household': cv_item.get('business_household', '')
                }
                csv_rows.append(row)
    
    return csv_rows

def save_to_csv(data: List[Dict[str, Any]], output_file: str):
    """保存数据到CSV文件"""
    if not data:
        print("没有数据可保存")
        return
    
    # 定义列顺序
    fieldnames = [
        'staff_code',
        'category', 
        'level_1',
        'level_2', 
        'level_3',
        'details',
        'business_family',
        'business_household'
    ]
    
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"数据已保存到 {output_file}")
    print(f"总共 {len(data)} 行数据")

def main():
    """主函数"""
    input_file = 'transformed_all.json'
    output_file = 'staff_cv_data.csv'
    
    try:
        # 加载JSON数据
        print("正在加载JSON数据...")
        json_data = load_json_data(input_file)
        print(f"加载了 {len(json_data)} 个员工的数据")
        
        # 转换为CSV格式
        print("正在转换数据格式...")
        csv_data = convert_to_csv_format(json_data)
        
        # 保存到CSV
        print("正在保存CSV文件...")
        save_to_csv(csv_data, output_file)
        
        # 显示统计信息
        df = pd.DataFrame(csv_data)
        print("\n=== 数据统计 ===")
        print(f"总行数: {len(df)}")
        print(f"员工数量: {df['staff_code'].nunique()}")
        print(f"有CV数据的员工: {len(df[df['category'] != ''])}")
        print(f"没有CV数据的员工: {len(df[df['category'] == ''])}")
        
        print("\n=== 类别分布 ===")
        category_counts = df[df['category'] != '']['category'].value_counts()
        for category, count in category_counts.head(10).items():
            print(f"{category}: {count}")
        
        print(f"\n转换完成！输出文件: {output_file}")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except json.JSONDecodeError:
        print(f"错误: {input_file} 不是有效的JSON文件")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
