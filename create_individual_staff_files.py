#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为每个员工创建单独的txt文件，用于RAG检索
每个文件包含该员工的完整简历信息，便于精准匹配
"""

import json
import os
from typing import List, Dict, Any

def load_json_data(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def format_skill_item(item: Dict[str, Any]) -> str:
    """格式化单个技能项目"""
    parts = []
    
    # 构建技能路径
    skill_path = []
    if item.get('level_1'):
        skill_path.append(item['level_1'])
    if item.get('level_2'):
        skill_path.append(item['level_2'])
    if item.get('level_3'):
        skill_path.append(item['level_3'])
    
    # 如果有业务分类信息，优先使用
    if item.get('business_family') and item.get('business_household'):
        skill_path = [item['business_family'], item['business_household']]
    elif item.get('business_family'):
        skill_path = [item['business_family']]
    
    # 构建技能描述
    if skill_path:
        skill_desc = " > ".join(skill_path)
        parts.append(f"- {skill_desc}")
    
    # 添加详细信息（如果有的话）
    if item.get('details'):
        details = item['details'].strip()
        if details:
            parts.append(f"  详细说明: {details}")
    
    return "\n".join(parts) if parts else ""

def format_category_skills(category: str, items: List[Dict[str, Any]]) -> str:
    """格式化某个类别下的所有技能"""
    if not items:
        return ""
    
    skills = []
    for item in items:
        skill_text = format_skill_item(item)
        if skill_text:
            skills.append(skill_text)
    
    if not skills:
        return ""
    
    # 保持完整的类别名称，便于理解
    category_display = category
    
    return f"\n## {category_display}\n\n" + "\n\n".join(skills)

def create_staff_content(staff: Dict[str, Any]) -> str:
    """创建单个员工的完整简历内容"""
    staff_code = staff.get('staff_code', '')
    staff_cv = staff.get('staff_cv', [])
    
    content = f"# 员工简历 - Staff {staff_code}\n\n"
    content += f"**员工编号**: {staff_code}\n\n"
    
    if not staff_cv:
        content += "暂无简历数据\n"
        return content
    
    # 按类别分组
    categories = {}
    for item in staff_cv:
        category = item.get('category', '其他')
        if category not in categories:
            categories[category] = []
        categories[category].append(item)
    
    # 定义类别优先级顺序
    category_order = [
        "具有实操能力的技术（能够实际操作的技术）",
        "具有理论储备的技术（能够谈但没有实际做过的技术）",
        "本人目前岗位涉及的业务方向和技术（操盘业务）",
        "具体参与过的项目涉及的研究领域",
        "具有理论储备的研究领域（能谈或者提供咨询，但没有具体研究过）",
        "能够主持发展的业务（可以做承包人的业务）",
        "可以整合的外部资源（外部合作伙伴、专家顾问）",
        "可以整合的外部资源",
        "具有理论储备的技术",
        "具有理论储备的研究领域（能谈但没有具体研究过）"
    ]
    
    # 按优先级顺序格式化各个类别
    for category in category_order:
        if category in categories:
            category_text = format_category_skills(category, categories[category])
            if category_text:
                content += category_text + "\n"
            del categories[category]
    
    # 处理剩余的类别
    for category, items in categories.items():
        if category:  # 跳过空类别
            category_text = format_category_skills(category, items)
            if category_text:
                content += category_text + "\n"
    
    return content

def create_individual_files(data: List[Dict[str, Any]], output_dir: str = "staff_profiles"):
    """为每个员工创建单独的txt文件"""
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    created_files = []
    has_data_count = 0
    no_data_count = 0
    
    for staff in data:
        staff_code = staff.get('staff_code', 'unknown')
        staff_cv = staff.get('staff_cv', [])
        
        # 创建文件内容
        content = create_staff_content(staff)
        
        # 文件名
        filename = f"staff_{staff_code}.txt"
        filepath = os.path.join(output_dir, filename)
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append(filename)
        
        # 统计
        if staff_cv:
            has_data_count += 1
        else:
            no_data_count += 1
    
    return created_files, has_data_count, no_data_count

def create_index_file(files: List[str], output_dir: str):
    """创建索引文件，列出所有员工文件"""
    index_content = "# 员工简历文件索引\n\n"
    index_content += f"总共 {len(files)} 个员工文件\n\n"
    index_content += "## 文件列表\n\n"
    
    for filename in sorted(files):
        staff_code = filename.replace('staff_', '').replace('.txt', '')
        index_content += f"- {filename} (员工编号: {staff_code})\n"
    
    index_path = os.path.join(output_dir, "index.txt")
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    return index_path

def main():
    """主函数"""
    input_file = 'transformed_all.json'
    output_dir = 'staff_profiles'
    
    try:
        # 加载JSON数据
        print("正在加载JSON数据...")
        json_data = load_json_data(input_file)
        print(f"加载了 {len(json_data)} 个员工的数据")
        
        # 创建单独的文件
        print("正在为每个员工创建单独的txt文件...")
        created_files, has_data_count, no_data_count = create_individual_files(json_data, output_dir)
        
        # 创建索引文件
        print("正在创建索引文件...")
        index_path = create_index_file(created_files, output_dir)
        
        print(f"\n=== 创建完成 ===")
        print(f"输出目录: {output_dir}")
        print(f"创建文件数: {len(created_files)}")
        print(f"有简历数据的员工: {has_data_count}")
        print(f"没有简历数据的员工: {no_data_count}")
        print(f"索引文件: {index_path}")
        
        # 显示几个示例文件
        print(f"\n=== 示例文件 ===")
        sample_files = [f for f in created_files if 'staff_125.txt' in f or 'staff_16.txt' in f][:2]
        for filename in sample_files:
            print(f"- {filename}")
        
        print(f"\n现在您可以将 {output_dir} 目录中的所有txt文件导入到FastGPT中进行RAG检索了！")
        print("每个文件代表一个员工的完整简历，检索时会返回对应的文件名，非常精准。")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except json.JSONDecodeError:
        print(f"错误: {input_file} 不是有效的JSON文件")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
