#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取指定员工ID的txt文件到新文件夹
"""

import os
import shutil
from typing import List

def extract_staff_files(staff_ids: List[str], source_dir: str = "staff_profiles", target_dir: str = "selected_staff"):
    """
    提取指定员工ID的文件到新文件夹
    
    Args:
        staff_ids: 员工ID列表
        source_dir: 源文件夹路径
        target_dir: 目标文件夹路径
    """
    
    # 创建目标目录
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"创建目录: {target_dir}")
    
    copied_files = []
    missing_files = []
    
    for staff_id in staff_ids:
        # 构建源文件路径
        source_file = os.path.join(source_dir, f"staff_{staff_id}.txt")
        target_file = os.path.join(target_dir, f"staff_{staff_id}.txt")
        
        # 检查源文件是否存在
        if os.path.exists(source_file):
            # 复制文件
            shutil.copy2(source_file, target_file)
            copied_files.append(f"staff_{staff_id}.txt")
            print(f"✓ 复制成功: staff_{staff_id}.txt")
        else:
            missing_files.append(staff_id)
            print(f"✗ 文件不存在: staff_{staff_id}.txt")
    
    return copied_files, missing_files

def create_selected_index(copied_files: List[str], target_dir: str):
    """创建选中员工的索引文件"""
    index_content = "# 选中员工简历文件索引\n\n"
    index_content += f"总共 {len(copied_files)} 个选中员工文件\n\n"
    index_content += "## 文件列表\n\n"
    
    for filename in sorted(copied_files):
        staff_id = filename.replace('staff_', '').replace('.txt', '')
        index_content += f"- {filename} (员工编号: {staff_id})\n"
    
    index_path = os.path.join(target_dir, "selected_index.txt")
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    return index_path

def main():
    """主函数"""
    # 指定的员工ID列表
    target_staff_ids = [
        "14", "141", "145", "154", "162", "246", "248", "249", 
        "307", "308", "31", "311", "337", "356", "387"
    ]
    
    source_directory = "staff_profiles"
    target_directory = "selected_staff"
    
    print("=== 提取指定员工文件 ===")
    print(f"源目录: {source_directory}")
    print(f"目标目录: {target_directory}")
    print(f"要提取的员工ID: {', '.join(target_staff_ids)}")
    print()
    
    # 检查源目录是否存在
    if not os.path.exists(source_directory):
        print(f"错误: 源目录 {source_directory} 不存在")
        return
    
    # 提取文件
    copied_files, missing_files = extract_staff_files(
        target_staff_ids, 
        source_directory, 
        target_directory
    )
    
    # 创建索引文件
    if copied_files:
        index_path = create_selected_index(copied_files, target_directory)
        print(f"\n索引文件已创建: {index_path}")
    
    # 显示结果统计
    print(f"\n=== 提取结果 ===")
    print(f"成功复制: {len(copied_files)} 个文件")
    print(f"文件不存在: {len(missing_files)} 个")
    
    if copied_files:
        print(f"\n成功复制的文件:")
        for filename in sorted(copied_files):
            print(f"  - {filename}")
    
    if missing_files:
        print(f"\n未找到的员工ID:")
        for staff_id in missing_files:
            print(f"  - {staff_id}")
    
    print(f"\n所有文件已保存到: {target_directory}")

if __name__ == "__main__":
    main()
